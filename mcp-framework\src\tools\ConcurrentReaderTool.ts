import { z } from 'zod';
import { MCPTool } from './BaseTool.js';
import { promises as fs } from 'fs';
import { join, dirname, basename, extname } from 'path';
import { spawn } from 'child_process';
import { promisify } from 'util';
import { glob } from 'glob';

const ConcurrentReaderSchema = z.object({
  files: z.array(z.string()).optional().describe("Array of file paths to read (if empty, discovers workspace files)"),
  maxWorkers: z.number().int().positive().optional().default(8).describe("Maximum number of worker threads for tag generation"),
  generateTags: z.boolean().optional().default(true).describe("Whether to generate ctags for the files"),
  maxDisplayLines: z.number().int().optional().describe("Maximum lines to display per file (undefined = show all)"),
  contextDir: z.string().optional().default("context").describe("Directory to save context files")
});

interface FileProcessingResult {
  path: string;
  status: 'success' | 'error';
  content?: string;
  outputFile?: string;
  lines?: number;
  sizeBytes?: number;
  error?: string;
}

interface TagGenerationResult {
  path: string;
  status: 'success' | 'error';
  outputFile?: string;
  tagsCount?: number;
  error?: string;
}

interface Tag {
  symbol: string;
  kind: string;
  line: string;
  pattern: string;
}

export default class ConcurrentReaderTool extends MCPTool {
  name = "concurrent_reader";
  description = "Concurrently read multiple files, generate tags, and save context data";
  schema = ConcurrentReaderSchema;

  private async ensureContextFolder(contextDir: string): Promise<string> {
    try {
      await fs.mkdir(contextDir, { recursive: true });
      return contextDir;
    } catch (error) {
      throw new Error(`Failed to create context directory: ${error}`);
    }
  }

  private async checkCtagsInstalled(): Promise<boolean> {
    return new Promise((resolve) => {
      const ctags = spawn('ctags', ['--version']);
      ctags.on('close', (code) => {
        resolve(code === 0);
      });
      ctags.on('error', () => {
        resolve(false);
      });
    });
  }

  private async readFileContentAndSave(filepath: string, contextDir: string): Promise<FileProcessingResult> {
    try {
      // Simulate I/O latency to demonstrate concurrency
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const content = await fs.readFile(filepath, 'utf-8');
      const baseFilename = basename(filepath);
      const contentFilename = `${baseFilename.replace(extname(baseFilename), '')}.content.json`;
      const contentPath = join(contextDir, contentFilename);
      
      const fileData = {
        path: filepath,
        filename: baseFilename,
        content: content,
        lines: content.split('\n').length,
        sizeBytes: Buffer.byteLength(content, 'utf-8')
      };
      
      await fs.writeFile(contentPath, JSON.stringify(fileData, null, 2));
      
      return {
        path: filepath,
        status: 'success',
        content: content,
        outputFile: contentPath,
        lines: fileData.lines,
        sizeBytes: fileData.sizeBytes
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      return {
        path: filepath,
        status: 'error',
        error: errorMsg
      };
    }
  }

  private async generateTagsForFile(filepath: string, contextDir: string): Promise<TagGenerationResult> {
    return new Promise((resolve) => {
      const tempTagFile = join(contextDir, `temp_${Date.now()}.tags`);
      
      const ctags = spawn('ctags', ['-f', tempTagFile, '--fields=+n', filepath]);
      
      ctags.on('close', async (code) => {
        if (code !== 0) {
          resolve({
            path: filepath,
            status: 'error',
            error: `ctags failed with exit code ${code}`
          });
          return;
        }
        
        try {
          const tagContent = await fs.readFile(tempTagFile, 'utf-8');
          const tags: Tag[] = [];
          
          const lines = tagContent.split('\n');
          for (const line of lines) {
            if (line.startsWith('!_TAG_') || !line.trim()) continue;
            
            const parts = line.split('\t');
            if (parts.length < 4) continue;
            
            const [symbol, file, pattern, meta] = parts;
            const kind = meta.includes(':') ? meta.split(':').pop() || meta : meta;
            
            tags.push({
              symbol,
              kind,
              line: pattern.replace(/^\/\^?|\$?\/$/, ''),
              pattern
            });
          }
          
          // Clean up temp file
          await fs.unlink(tempTagFile);
          
          // Save tags to JSON file
          const baseFilename = basename(filepath);
          const tagFilename = `${baseFilename.replace(extname(baseFilename), '')}.tags.json`;
          const tagPath = join(contextDir, tagFilename);
          
          await fs.writeFile(tagPath, JSON.stringify(tags, null, 2));
          
          resolve({
            path: filepath,
            status: 'success',
            outputFile: tagPath,
            tagsCount: tags.length
          });
        } catch (error) {
          resolve({
            path: filepath,
            status: 'error',
            error: error instanceof Error ? error.message : String(error)
          });
        }
      });
      
      ctags.on('error', (error) => {
        resolve({
          path: filepath,
          status: 'error',
          error: error.message
        });
      });
    });
  }

  private async getWorkspaceFiles(): Promise<string[]> {
    const patterns = [
      '*.py', '*.js', '*.ts', '*.jsx', '*.tsx', '*.md', '*.txt',
      '*.json', '*.yaml', '*.yml', '*.ini', '*.cfg', '*.toml'
    ];
    
    const files: string[] = [];
    for (const pattern of patterns) {
      const matches = await glob(pattern, { cwd: process.cwd() });
      files.push(...matches.map(f => join(process.cwd(), f)));
    }
    
    // Filter to only include actual files
    const validFiles: string[] = [];
    for (const file of files) {
      try {
        const stats = await fs.stat(file);
        if (stats.isFile()) {
          validFiles.push(file);
        }
      } catch {
        // Skip files that can't be accessed
      }
    }
    
    return validFiles;
  }

  async execute(input: z.infer<typeof ConcurrentReaderSchema>) {
    const startTime = Date.now();
    
    // Determine files to process
    let filePaths = input.files || [];
    if (filePaths.length === 0) {
      filePaths = await this.getWorkspaceFiles();
      if (filePaths.length === 0) {
        return this.createErrorResponse(new Error("No files found to process"));
      }
    }
    
    // Ensure context folder exists
    const contextDir = await this.ensureContextFolder(input.contextDir);
    
    // Check ctags availability
    const ctagsAvailable = input.generateTags ? await this.checkCtagsInstalled() : false;
    const shouldGenerateTags = input.generateTags && ctagsAvailable;
    
    // Process files concurrently
    const readPromises = filePaths.map(fp => this.readFileContentAndSave(fp, contextDir));
    const tagPromises = shouldGenerateTags 
      ? filePaths.map(fp => this.generateTagsForFile(fp, contextDir))
      : [];
    
    // Wait for all operations to complete
    const [readResults, tagResults] = await Promise.all([
      Promise.all(readPromises),
      Promise.all(tagPromises)
    ]);
    
    const endTime = Date.now();
    const totalTime = (endTime - startTime) / 1000;
    
    // Generate summary
    const successfulReads = readResults.filter(r => r.status === 'success').length;
    const failedReads = readResults.filter(r => r.status === 'error').length;
    const successfulTags = tagResults.filter(r => r.status === 'success').length;
    
    const summary = {
      processedFiles: filePaths.length,
      successfulReads,
      failedReads,
      tagGenerationEnabled: input.generateTags,
      ctagsAvailable,
      successfulTags,
      processingTimeSeconds: totalTime,
      contextFolder: contextDir,
      readResults,
      tagResults
    };
    
    // Save summary
    const summaryPath = join(contextDir, 'processing_summary.json');
    await fs.writeFile(summaryPath, JSON.stringify(summary, null, 2));
    
    // Format response
    let response = `🚀 Concurrent File Reader Results\n`;
    response += `${'='.repeat(50)}\n`;
    response += `📁 Files processed: ${filePaths.length}\n`;
    response += `✅ Successfully read: ${successfulReads}\n`;
    response += `❌ Failed to read: ${failedReads}\n`;
    
    if (shouldGenerateTags) {
      response += `🏷️ Tags generated: ${successfulTags}/${filePaths.length}\n`;
    } else if (input.generateTags && !ctagsAvailable) {
      response += `⚠️ ctags not available - tag generation skipped\n`;
    }
    
    response += `⏱️ Total time: ${totalTime.toFixed(4)} seconds\n`;
    response += `📊 Average per file: ${(totalTime / filePaths.length).toFixed(4)} seconds\n`;
    response += `📁 Context folder: ${contextDir}\n`;
    response += `📋 Summary saved: ${summaryPath}\n\n`;
    
    // Show file contents if requested
    if (input.maxDisplayLines !== 0) {
      response += `📄 FILE CONTENTS\n`;
      response += `${'='.repeat(50)}\n`;
      
      for (const result of readResults) {
        const filename = basename(result.path);
        
        if (result.status === 'error') {
          response += `\n❌ ${filename}: ${result.error}\n`;
        } else if (result.content) {
          response += `\n📖 ${filename} (${result.sizeBytes} bytes, ${result.lines} lines)\n`;
          response += `${'-'.repeat(40)}\n`;
          
          const lines = result.content.split('\n');
          const maxLines = input.maxDisplayLines;
          
          if (maxLines && lines.length > maxLines) {
            response += `First ${maxLines} lines (of ${lines.length} total):\n`;
            lines.slice(0, maxLines).forEach((line, i) => {
              response += `${String(i + 1).padStart(3)}: ${line}\n`;
            });
            response += `... (${lines.length - maxLines} more lines)\n`;
          } else {
            lines.forEach((line, i) => {
              response += `${String(i + 1).padStart(3)}: ${line}\n`;
            });
          }
        }
      }
    }
    
    return response;
  }
} 