import { promises as fs } from 'fs';
import { join } from 'path';
import { tmpdir } from 'os';
import ConcurrentReaderTool from '../../src/tools/ConcurrentReaderTool.js';

describe('ConcurrentReaderTool', () => {
  let tool: ConcurrentReaderTool;
  let tempDir: string;
  let testFiles: string[];

  beforeEach(async () => {
    tool = new ConcurrentReaderTool();
    
    // Create a temporary directory for test files
    tempDir = join(tmpdir(), `concurrent-reader-test-${Date.now()}`);
    await fs.mkdir(tempDir, { recursive: true });
    
    // Create some test files
    testFiles = [
      join(tempDir, 'test1.js'),
      join(tempDir, 'test2.ts'),
      join(tempDir, 'test3.md')
    ];
    
    await fs.writeFile(testFiles[0], `// Test JavaScript file
function hello() {
  console.log("Hello from JS");
}

module.exports = { hello };`);
    
    await fs.writeFile(testFiles[1], `// Test TypeScript file
interface User {
  name: string;
  age: number;
}

export function greet(user: User): string {
  return \`Hello, \${user.name}!\`;
}`);
    
    await fs.writeFile(testFiles[2], `# Test Markdown File

This is a test markdown file for the concurrent reader tool.

## Features

- Reading multiple files
- Concurrent processing
- Tag generation (if ctags available)`);
  });

  afterEach(async () => {
    // Clean up test files
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('Tool Definition', () => {
    it('should have correct name and description', () => {
      expect(tool.name).toBe('concurrent_reader');
      expect(tool.description).toBe('Concurrently read multiple files, generate tags, and save context data');
    });

    it('should have valid schema', () => {
      const definition = tool.getToolDefinition();
      expect(definition.name).toBe('concurrent_reader');
      expect(definition.inputSchema).toBeDefined();
      expect(definition.inputSchema.properties).toBeDefined();
    });
  });

  describe('File Reading', () => {
    it('should read specific files when provided', async () => {
      const response = await tool.toolCall({
        params: {
          name: 'concurrent_reader',
          arguments: {
            files: testFiles,
            generateTags: false,
            maxDisplayLines: 5,
            contextDir: join(tempDir, 'context')
          }
        }
      });

      expect(response.content).toHaveLength(1);
      expect(response.content[0].type).toBe('text');
      
      const result = (response.content[0] as any).text;
      expect(result).toContain('Files processed: 3');
      expect(result).toContain('Successfully read: 3');
      expect(result).toContain('Failed to read: 0');
      expect(result).toContain('test1.js');
      expect(result).toContain('test2.ts');
      expect(result).toContain('test3.md');
    });

    it('should handle non-existent files gracefully', async () => {
      const nonExistentFiles = [
        join(tempDir, 'nonexistent1.js'),
        join(tempDir, 'nonexistent2.ts')
      ];

      const response = await tool.toolCall({
        params: {
          name: 'concurrent_reader',
          arguments: {
            files: nonExistentFiles,
            generateTags: false,
            contextDir: join(tempDir, 'context')
          }
        }
      });

      expect(response.content).toHaveLength(1);
      const result = (response.content[0] as any).text;
      expect(result).toContain('Files processed: 2');
      expect(result).toContain('Failed to read: 2');
    });

    it('should limit displayed lines when maxDisplayLines is set', async () => {
      const response = await tool.toolCall({
        params: {
          name: 'concurrent_reader',
          arguments: {
            files: [testFiles[1]], // TypeScript file with multiple lines
            generateTags: false,
            maxDisplayLines: 3,
            contextDir: join(tempDir, 'context')
          }
        }
      });

      const result = (response.content[0] as any).text;
      expect(result).toContain('First 3 lines');
      expect(result).toContain('more lines');
    });

    it('should not display file contents when maxDisplayLines is 0', async () => {
      const response = await tool.toolCall({
        params: {
          name: 'concurrent_reader',
          arguments: {
            files: testFiles,
            generateTags: false,
            maxDisplayLines: 0,
            contextDir: join(tempDir, 'context')
          }
        }
      });

      const result = (response.content[0] as any).text;
      expect(result).not.toContain('FILE CONTENTS');
      expect(result).not.toContain('test1.js');
    });
  });

  describe('Context Files', () => {
    it('should create context directory and save files', async () => {
      const contextDir = join(tempDir, 'context');
      
      await tool.toolCall({
        params: {
          name: 'concurrent_reader',
          arguments: {
            files: testFiles,
            generateTags: false,
            contextDir
          }
        }
      });

      // Check that context directory was created
      const contextExists = await fs.access(contextDir).then(() => true).catch(() => false);
      expect(contextExists).toBe(true);

      // Check that content files were created
      const contentFiles = await fs.readdir(contextDir);
      expect(contentFiles).toContain('test1.content.json');
      expect(contentFiles).toContain('test2.content.json');
      expect(contentFiles).toContain('test3.content.json');
      expect(contentFiles).toContain('processing_summary.json');

      // Verify content of one file
      const test1Content = JSON.parse(await fs.readFile(join(contextDir, 'test1.content.json'), 'utf-8'));
      expect(test1Content.path).toBe(testFiles[0]);
      expect(test1Content.filename).toBe('test1.js');
      expect(test1Content.content).toContain('function hello()');
      expect(test1Content.lines).toBeGreaterThan(0);
      expect(test1Content.sizeBytes).toBeGreaterThan(0);
    });

    it('should save processing summary with correct statistics', async () => {
      const contextDir = join(tempDir, 'context');
      
      await tool.toolCall({
        params: {
          name: 'concurrent_reader',
          arguments: {
            files: testFiles,
            generateTags: false,
            contextDir
          }
        }
      });

      const summaryContent = JSON.parse(await fs.readFile(join(contextDir, 'processing_summary.json'), 'utf-8'));
      expect(summaryContent.processedFiles).toBe(3);
      expect(summaryContent.successfulReads).toBe(3);
      expect(summaryContent.failedReads).toBe(0);
      expect(summaryContent.tagGenerationEnabled).toBe(false);
      expect(summaryContent.processingTimeSeconds).toBeGreaterThan(0);
      expect(summaryContent.contextFolder).toBe(contextDir);
      expect(summaryContent.readResults).toHaveLength(3);
    });
  });

  describe('Error Handling', () => {
    it('should handle empty file list gracefully when no workspace files found', async () => {
      // Change to a directory with no matching files
      const originalCwd = process.cwd();
      process.chdir(tempDir);
      
      try {
        const response = await tool.toolCall({
          params: {
            name: 'concurrent_reader',
            arguments: {
              generateTags: false,
              contextDir: join(tempDir, 'context')
            }
          }
        });

        expect(response.content).toHaveLength(1);
        expect(response.content[0].type).toBe('text');
        const result = (response.content[0] as any).text;
        expect(result).toContain('error');
      } finally {
        process.chdir(originalCwd);
      }
    });
  });
});
