// HTTP client to test our MCP server with HTTP stream transport
import { randomUUID } from 'crypto';

const SERVER_URL = 'http://localhost:3000/mcp';
let sessionId = null;

async function sendRequest(method, params = {}) {
  const request = {
    jsonrpc: "2.0",
    id: randomUUID(),
    method: method,
    params: params
  };

  const headers = {
    'Content-Type': 'application/json',
  };

  if (sessionId) {
    headers['Mcp-Session-Id'] = sessionId;
  }

  console.log(`\n=== Sending ${method} ===`);
  console.log('Request:', JSON.stringify(request, null, 2));

  try {
    const response = await fetch(SERVER_URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    // Check if we got a new session ID
    const newSessionId = response.headers.get('Mcp-Session-Id');
    if (newSessionId && !sessionId) {
      sessionId = newSessionId;
      console.log(`Session ID received: ${sessionId}`);
    }

    const result = await response.json();
    console.log('Response:', JSON.stringify(result, null, 2));
    return result;
  } catch (error) {
    console.error('Error:', error.message);
    return null;
  }
}

async function testMCPServer() {
  console.log('=== Testing MCP Server with HTTP Stream Transport ===\n');

  // 1. Initialize the connection
  console.log('1. Initializing connection...');
  await sendRequest('initialize', {
    protocolVersion: "2024-11-05",
    capabilities: {
      tools: {}
    },
    clientInfo: {
      name: "test-http-client",
      version: "1.0.0"
    }
  });

  // Wait a bit for initialization
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 2. List available tools
  console.log('\n2. Listing available tools...');
  await sendRequest('tools/list');

  // Wait a bit
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 3. Test our concurrent reader tool
  console.log('\n3. Testing concurrent_reader tool...');
  await sendRequest('tools/call', {
    name: 'concurrent_reader',
    arguments: {
      files: [
        'test-file1.js',
        'test-file2.ts',
        'README.md'
      ],
      generateTags: false,
      maxDisplayLines: 5,
      contextDir: 'http-test-output'
    }
  });

  // Wait a bit
  await new Promise(resolve => setTimeout(resolve, 2000));

  // 4. Test example tool
  console.log('\n4. Testing example tool...');
  await sendRequest('tools/call', {
    name: 'example_tool',
    arguments: {
      message: 'Hello from HTTP client!'
    }
  });

  console.log('\n=== Tests completed ===');
}

// Run the tests
testMCPServer().catch(console.error);
