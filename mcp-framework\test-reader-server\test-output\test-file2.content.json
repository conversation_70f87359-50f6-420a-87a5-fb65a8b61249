{"path": "C:\\Users\\<USER>\\Downloads\\mcp-server\\mcp-framework\\test-reader-server\\test-file2.ts", "filename": "test-file2.ts", "content": "// Test TypeScript file for concurrent reader\ninterface User {\n    id: number;\n    name: string;\n    email: string;\n    isActive: boolean;\n}\n\ninterface ApiResponse<T> {\n    data: T;\n    status: number;\n    message: string;\n}\n\nclass UserService {\n    private users: User[] = [];\n    \n    constructor() {\n        this.users = [];\n    }\n    \n    addUser(user: Omit<User, 'id'>): User {\n        const newUser: User = {\n            id: this.users.length + 1,\n            ...user\n        };\n        this.users.push(newUser);\n        return newUser;\n    }\n    \n    getUserById(id: number): User | undefined {\n        return this.users.find(user => user.id === id);\n    }\n    \n    getAllUsers(): User[] {\n        return this.users.filter(user => user.isActive);\n    }\n    \n    updateUser(id: number, updates: Partial<User>): User | null {\n        const userIndex = this.users.findIndex(user => user.id === id);\n        if (userIndex === -1) return null;\n        \n        this.users[userIndex] = { ...this.users[userIndex], ...updates };\n        return this.users[userIndex];\n    }\n}\n\nexport { User, ApiResponse, UserService };\n", "lines": 49, "sizeBytes": 1119}