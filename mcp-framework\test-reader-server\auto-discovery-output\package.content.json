{"path": "C:\\Users\\<USER>\\Downloads\\mcp-server\\mcp-framework\\test-reader-server\\package.json", "filename": "package.json", "content": "{\n  \"name\": \"test-reader-server\",\n  \"version\": \"0.0.1\",\n  \"description\": \"test-reader-server MCP server\",\n  \"type\": \"module\",\n  \"bin\": {\n    \"test-reader-server\": \"./dist/index.js\"\n  },\n  \"files\": [\n    \"dist\"\n  ],\n  \"scripts\": {\n    \"build\": \"tsc && mcp-build\",\n    \"watch\": \"tsc --watch\",\n    \"start\": \"node dist/index.js\"\n  },\n  \"dependencies\": {\n    \"mcp-framework\": \"^0.2.2\"\n  },\n  \"devDependencies\": {\n    \"@types/node\": \"^20.11.24\",\n    \"typescript\": \"^5.3.3\"\n  },\n  \"engines\": {\n    \"node\": \">=18.19.0\"\n  }\n}", "lines": 27, "sizeBytes": 516}