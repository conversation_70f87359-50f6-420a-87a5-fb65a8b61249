{"path": "C:\\Users\\<USER>\\Downloads\\mcp-server\\mcp-framework\\test-reader-server\\test-file1.js", "filename": "test-file1.js", "content": "// Test JavaScript file for concurrent reader\nfunction greetUser(name) {\n    console.log(`Hello, ${name}!`);\n    return `Greeting sent to ${name}`;\n}\n\nclass Calculator {\n    constructor() {\n        this.result = 0;\n    }\n    \n    add(num) {\n        this.result += num;\n        return this;\n    }\n    \n    multiply(num) {\n        this.result *= num;\n        return this;\n    }\n    \n    getResult() {\n        return this.result;\n    }\n}\n\nmodule.exports = { greetUser, Calculator };\n", "lines": 28, "sizeBytes": 480}