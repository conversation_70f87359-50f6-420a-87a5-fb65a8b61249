// Simple test client to test our concurrent reader tool
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Start the MCP server
const serverProcess = spawn('node', ['dist/index.js'], {
  cwd: __dirname,
  stdio: ['pipe', 'pipe', 'pipe']
});

let requestId = 1;

function sendRequest(method, params = {}) {
  const request = {
    jsonrpc: "2.0",
    id: requestId++,
    method: method,
    params: params
  };
  
  console.log('Sending request:', JSON.stringify(request, null, 2));
  serverProcess.stdin.write(JSON.stringify(request) + '\n');
}

function handleResponse(data) {
  const lines = data.toString().split('\n').filter(line => line.trim());
  
  for (const line of lines) {
    try {
      const response = JSON.parse(line);
      console.log('Received response:', JSON.stringify(response, null, 2));
    } catch (e) {
      // Ignore non-JSON lines (like log messages)
      if (line.includes('[INFO]') || line.includes('[ERROR]')) {
        console.log('Server log:', line);
      }
    }
  }
}

serverProcess.stdout.on('data', handleResponse);
serverProcess.stderr.on('data', (data) => {
  console.error('Server error:', data.toString());
});

// Wait a bit for server to start, then send requests
setTimeout(() => {
  console.log('=== Testing MCP Server ===\n');
  
  // 1. Initialize the connection
  sendRequest('initialize', {
    protocolVersion: "2024-11-05",
    capabilities: {
      tools: {}
    },
    clientInfo: {
      name: "test-client",
      version: "1.0.0"
    }
  });
  
  // 2. List available tools
  setTimeout(() => {
    sendRequest('tools/list');
  }, 1000);
  
  // 3. Test our concurrent reader tool
  setTimeout(() => {
    sendRequest('tools/call', {
      name: 'concurrent_reader',
      arguments: {
        files: [
          join(__dirname, 'test-file1.js'),
          join(__dirname, 'test-file2.ts'),
          join(__dirname, 'README.md')
        ],
        generateTags: false,
        maxDisplayLines: 5,
        contextDir: 'test-output'
      }
    });
  }, 2000);
  
  // 4. Test auto-discovery (no files specified)
  setTimeout(() => {
    sendRequest('tools/call', {
      name: 'concurrent_reader',
      arguments: {
        generateTags: false,
        maxDisplayLines: 3,
        contextDir: 'auto-discovery-output'
      }
    });
  }, 4000);
  
  // Close after tests
  setTimeout(() => {
    console.log('\n=== Tests completed ===');
    serverProcess.kill();
    process.exit(0);
  }, 8000);
  
}, 1000);
