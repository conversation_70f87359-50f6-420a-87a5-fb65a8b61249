{"name": "test-reader-server", "version": "0.0.1", "description": "test-reader-server MCP server", "type": "module", "bin": {"test-reader-server": "./dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && mcp-build", "watch": "tsc --watch", "start": "node dist/index.js"}, "dependencies": {"mcp-framework": "^0.2.2"}, "devDependencies": {"@types/node": "^20.11.24", "typescript": "^5.3.3"}, "engines": {"node": ">=18.19.0"}}