{"name": "mcp-framework", "version": "0.2.15", "description": "Framework for building Model Context Protocol (MCP) servers in Typescript", "type": "module", "author": "<PERSON> <<EMAIL>>", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "files": ["dist"], "bin": {"mcp": "dist/cli/index.js", "mcp-build": "dist/cli/framework/build-cli.js"}, "scripts": {"build": "tsc", "watch": "tsc --watch", "lint": "eslint", "lint:fix": "eslint --fix", "format": "prettier --write \"src/**/*.ts\"", "prepare": "npm run build", "dev:pub": "rm -rf dist && npm run build && yalc publish --push", "test": "NODE_OPTIONS='--experimental-vm-modules' jest", "test:watch": "NODE_OPTIONS='--experimental-vm-modules' jest --watch", "test:coverage": "NODE_OPTIONS='--experimental-vm-modules' jest --coverage"}, "engines": {"node": ">=18.19.0"}, "keywords": ["mcp", "claude", "anthropic", "ai", "framework", "tools", "modelcontextprotocol", "model", "context", "protocol"], "peerDependencies": {"@modelcontextprotocol/sdk": "^1.11.0"}, "dependencies": {"@types/prompts": "^2.4.9", "commander": "^12.1.0", "content-type": "^1.0.5", "execa": "^9.5.2", "find-up": "^7.0.0", "glob": "^11.0.3", "jsonwebtoken": "^9.0.2", "prompts": "^2.4.2", "raw-body": "^2.5.2", "typescript": "^5.3.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.23.0", "@types/content-type": "^1.1.8", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.8", "@types/node": "^20.17.28", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.5", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.5.3", "ts-jest": "^29.1.2", "typescript-eslint": "^8.28.0"}}