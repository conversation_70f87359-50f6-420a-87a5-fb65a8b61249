{"path": "C:\\Users\\<USER>\\Downloads\\mcp-server\\mcp-framework\\test-reader-server\\test-client.js", "filename": "test-client.js", "content": "// Simple test client to test our concurrent reader tool\nimport { spawn } from 'child_process';\nimport { fileURLToPath } from 'url';\nimport { dirname, join } from 'path';\n\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = dirname(__filename);\n\n// Start the MCP server\nconst serverProcess = spawn('node', ['dist/index.js'], {\n  cwd: __dirname,\n  stdio: ['pipe', 'pipe', 'pipe']\n});\n\nlet requestId = 1;\n\nfunction sendRequest(method, params = {}) {\n  const request = {\n    jsonrpc: \"2.0\",\n    id: requestId++,\n    method: method,\n    params: params\n  };\n  \n  console.log('Sending request:', JSON.stringify(request, null, 2));\n  serverProcess.stdin.write(JSON.stringify(request) + '\\n');\n}\n\nfunction handleResponse(data) {\n  const lines = data.toString().split('\\n').filter(line => line.trim());\n  \n  for (const line of lines) {\n    try {\n      const response = JSON.parse(line);\n      console.log('Received response:', JSON.stringify(response, null, 2));\n    } catch (e) {\n      // Ignore non-JSON lines (like log messages)\n      if (line.includes('[INFO]') || line.includes('[ERROR]')) {\n        console.log('Server log:', line);\n      }\n    }\n  }\n}\n\nserverProcess.stdout.on('data', handleResponse);\nserverProcess.stderr.on('data', (data) => {\n  console.error('Server error:', data.toString());\n});\n\n// Wait a bit for server to start, then send requests\nsetTimeout(() => {\n  console.log('=== Testing MCP Server ===\\n');\n  \n  // 1. Initialize the connection\n  sendRequest('initialize', {\n    protocolVersion: \"2024-11-05\",\n    capabilities: {\n      tools: {}\n    },\n    clientInfo: {\n      name: \"test-client\",\n      version: \"1.0.0\"\n    }\n  });\n  \n  // 2. List available tools\n  setTimeout(() => {\n    sendRequest('tools/list');\n  }, 1000);\n  \n  // 3. Test our concurrent reader tool\n  setTimeout(() => {\n    sendRequest('tools/call', {\n      name: 'concurrent_reader',\n      arguments: {\n        files: [\n          join(__dirname, 'test-file1.js'),\n          join(__dirname, 'test-file2.ts'),\n          join(__dirname, 'README.md')\n        ],\n        generateTags: false,\n        maxDisplayLines: 5,\n        contextDir: 'test-output'\n      }\n    });\n  }, 2000);\n  \n  // 4. Test auto-discovery (no files specified)\n  setTimeout(() => {\n    sendRequest('tools/call', {\n      name: 'concurrent_reader',\n      arguments: {\n        generateTags: false,\n        maxDisplayLines: 3,\n        contextDir: 'auto-discovery-output'\n      }\n    });\n  }, 4000);\n  \n  // Close after tests\n  setTimeout(() => {\n    console.log('\\n=== Tests completed ===');\n    serverProcess.kill();\n    process.exit(0);\n  }, 8000);\n  \n}, 1000);\n", "lines": 108, "sizeBytes": 2631}