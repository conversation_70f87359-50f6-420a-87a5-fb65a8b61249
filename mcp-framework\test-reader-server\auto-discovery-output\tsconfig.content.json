{"path": "C:\\Users\\<USER>\\Downloads\\mcp-server\\mcp-framework\\test-reader-server\\tsconfig.json", "filename": "tsconfig.json", "content": "{\n  \"compilerOptions\": {\n    \"target\": \"ESNext\",\n    \"module\": \"ESNext\",\n    \"moduleResolution\": \"node\",\n    \"outDir\": \"./dist\",\n    \"rootDir\": \"./src\",\n    \"strict\": true,\n    \"esModuleInterop\": true,\n    \"skipLibCheck\": true,\n    \"forceConsistentCasingInFileNames\": true,\n    \"types\": []\n  },\n  \"include\": [\n    \"src/**/*\"\n  ],\n  \"exclude\": [\n    \"node_modules\"\n  ]\n}", "lines": 20, "sizeBytes": 369}